import re
import sys

def replace_base64_in_json(input_file, output_file):
    # 匹配 "profile_image_url":"base64数据" 或 "profile_image_url":"data:image/..." 的模式
    pattern = r'"profile_image_url"\s*:\s*"(?:data:image/[^;]+;base64,[^"]*|[A-Za-z0-9+/=]{20,})"'
    replacement = '"profile_image_url":"/static/favicon.png"'
    
    with open(input_file, 'r', encoding='utf-8') as infile, \
         open(output_file, 'w', encoding='utf-8') as outfile:
        
        count = 0
        for line in infile:
            # 替换当前行中的base64图片
            modified_line, replacements = re.subn(pattern, replacement, line)
            count += replacements
            outfile.write(modified_line)
    
    print(f"替换完成: {input_file} -> {output_file}")
    print(f"共替换了 {count} 个base64图片")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python replace_base64.py input.json output.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    replace_base64_in_json(input_file, output_file)